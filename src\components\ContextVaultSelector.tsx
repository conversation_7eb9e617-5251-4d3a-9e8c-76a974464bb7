import React, { useState, useEffect, useRef } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faChevronDown,
  faChevronLeft,
  faChevronRight,
  faPlus,
  faFolder,
  faTimes
} from '@fortawesome/free-solid-svg-icons'
import { ContextVault, ContextFolder } from '../types'
import { contextVaultService } from '../services/contextVaultService'

interface ContextVaultSelectorProps {
  selectedContextId?: string
  onContextChange?: (contextId: string) => void
}

interface NewVaultFormData {
  name: string
  objective: string
  vaultType: 'Personal' | 'Work'
}

export const ContextVaultSelector: React.FC<ContextVaultSelectorProps> = ({
  selectedContextId,
  onContextChange
}) => {
  const [vaults, setVaults] = useState<ContextVault[]>([])
  const [currentVaultIndex, setCurrentVaultIndex] = useState(0)
  const [selectedContext, setSelectedContext] = useState<ContextFolder | null>(null)
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [showNewVaultForm, setShowNewVaultForm] = useState(false)
  const [newVaultForm, setNewVaultForm] = useState<NewVaultFormData>({ name: '', objective: '', vaultType: 'Personal' })
  const [loading, setLoading] = useState(false)

  const dropdownRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Subscribe to context vault service
    const unsubscribe = contextVaultService.subscribe((updatedVaults, updatedSelectedContextId) => {
      setVaults(updatedVaults)

      // Update selected context if provided
      if (updatedSelectedContextId) {
        const result = contextVaultService.findContextById(updatedSelectedContextId)
        if (result) {
          setSelectedContext(result.context)
          const vaultIndex = updatedVaults.indexOf(result.vault)
          if (vaultIndex >= 0) {
            setCurrentVaultIndex(vaultIndex)
          }
        }
      }
    })

    // Initialize the service
    contextVaultService.initialize()

    return unsubscribe
  }, [])

  useEffect(() => {
    // Handle external selectedContextId changes
    if (selectedContextId) {
      const result = contextVaultService.findContextById(selectedContextId)
      if (result) {
        setSelectedContext(result.context)
        const vaultIndex = vaults.indexOf(result.vault)
        if (vaultIndex >= 0) {
          setCurrentVaultIndex(vaultIndex)
        }
      }
    }
  }, [selectedContextId, vaults])

  useEffect(() => {
    // Close dropdown when clicking outside
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const navigateVault = (direction: 'prev' | 'next') => {
    if (vaults.length === 0) return

    let newIndex = currentVaultIndex
    if (direction === 'prev') {
      newIndex = currentVaultIndex > 0 ? currentVaultIndex - 1 : vaults.length - 1
    } else {
      newIndex = currentVaultIndex < vaults.length - 1 ? currentVaultIndex + 1 : 0
    }

    setCurrentVaultIndex(newIndex)

    // Auto-select first context in new vault
    if (vaults[newIndex].contexts.length > 0) {
      const firstContext = vaults[newIndex].contexts[0]
      setSelectedContext(firstContext)
      contextVaultService.setSelectedContext(firstContext.id)
      onContextChange?.(firstContext.id)
    }
  }

  const selectContext = (context: ContextFolder) => {
    setSelectedContext(context)
    setIsDropdownOpen(false)
    contextVaultService.setSelectedContext(context.id)
    onContextChange?.(context.id)
  }

  const handleNewVault = async () => {
    if (!newVaultForm.name.trim()) return

    try {
      setLoading(true)

      const result = await contextVaultService.createContext({
        name: newVaultForm.name,
        objective: newVaultForm.objective,
        vaultType: newVaultForm.vaultType
      })

      if (result.success && result.context) {
        // Reset form and close modal
        setNewVaultForm({ name: '', objective: '', vaultType: 'Personal' })
        setShowNewVaultForm(false)

        // The context vault service will handle updating the UI and showing toast
        onContextChange?.(result.context.id)
      }

    } catch (error) {
      console.error('Error creating new context:', error)
    } finally {
      setLoading(false)
    }
  }

  const currentVault = vaults[currentVaultIndex]
  const availableContexts = currentVault?.contexts || []

  return (
    <>
      <div className="p-4 border-b border-tertiary/50">
        <div className="flex items-center justify-between">
          {/* Navigation and Dropdown */}
          <div className="flex items-center gap-2 flex-1">
            <button
              onClick={() => navigateVault('prev')}
              className="p-1 hover:bg-gray-700 rounded transition-colors"
              disabled={vaults.length <= 1}
            >
              <FontAwesomeIcon icon={faChevronLeft} className="text-gray-400 text-xs" />
            </button>

            <div className="relative flex-1" ref={dropdownRef}>
              <button
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                className="w-full flex items-center justify-between p-2 hover:bg-gray-700/50 rounded transition-colors"
              >
                <span className="font-medium text-supplement1 text-sm truncate">
                  {selectedContext?.name || 'Select Context'}
                </span>
                <FontAwesomeIcon
                  icon={faChevronDown}
                  className={`text-gray-400 text-xs transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`}
                />
              </button>


              {isDropdownOpen && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-gray-800 border border-tertiary/50 rounded-lg shadow-lg z-50 max-h-64 overflow-y-auto">
                  {availableContexts.map((context) => (
                    <button
                      key={context.id}
                      onClick={() => selectContext(context)}
                      className={`w-full text-left p-3 hover:bg-gray-700/50 transition-colors flex items-center gap-2 ${
                        selectedContext?.id === context.id ? 'bg-primary/20 text-primary' : 'text-supplement1'
                      }`}
                    >
                      <FontAwesomeIcon icon={faFolder} className="text-xs" />
                      <span className="text-sm">{context.name}</span>
                    </button>
                  ))}
                </div>
              )}
            </div>

            <button
              onClick={() => navigateVault('next')}
              className="p-1 hover:bg-gray-700 rounded transition-colors"
              disabled={vaults.length <= 1}
            >
              <FontAwesomeIcon icon={faChevronRight} className="text-gray-400 text-xs" />
            </button>
          </div>

          {/* Add New Vault Button */}
          <button
            onClick={() => setShowNewVaultForm(true)}
            className="p-1 hover:bg-gray-700 rounded transition-colors group relative ml-2"
          >
            <FontAwesomeIcon icon={faPlus} className="text-gray-400 text-xs" />
            <div className="absolute bottom-6 right-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              New Context
            </div>
          </button>
        </div>
      </div>

      {/* New Vault Form Overlay */}
      {showNewVaultForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-96 max-w-[90vw]">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-supplement1">New Context</h3>
              <button
                onClick={() => setShowNewVaultForm(false)}
                className="p-1 hover:bg-gray-700 rounded transition-colors"
              >
                <FontAwesomeIcon icon={faTimes} className="text-gray-400 text-sm" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-supplement1 mb-2">
                  Vault Type <span className="text-secondary">*</span>
                </label>
                <div className="flex gap-2">
                  <button
                    type="button"
                    onClick={() => setNewVaultForm(prev => ({ ...prev, vaultType: 'Personal' }))}
                    className={`flex-1 p-3 rounded-lg border transition-colors ${
                      newVaultForm.vaultType === 'Personal'
                        ? 'bg-primary/20 border-primary text-primary'
                        : 'bg-gray-700 border-tertiary/50 text-supplement1 hover:bg-gray-600'
                    }`}
                  >
                    Personal
                  </button>
                  <button
                    type="button"
                    onClick={() => setNewVaultForm(prev => ({ ...prev, vaultType: 'Work' }))}
                    className={`flex-1 p-3 rounded-lg border transition-colors ${
                      newVaultForm.vaultType === 'Work'
                        ? 'bg-secondary/20 border-secondary text-secondary'
                        : 'bg-gray-700 border-tertiary/50 text-supplement1 hover:bg-gray-600'
                    }`}
                  >
                    Work
                  </button>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-supplement1 mb-2">
                  Name <span className="text-secondary">*</span>
                </label>
                <input
                  type="text"
                  value={newVaultForm.name}
                  onChange={(e) => setNewVaultForm(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full p-3 bg-gray-700 border border-tertiary/50 rounded-lg text-supplement1 placeholder-gray-400 focus:outline-none focus:border-primary"
                  placeholder="Enter context name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-supplement1 mb-2">
                  Objective (optional)
                </label>
                <textarea
                  value={newVaultForm.objective}
                  onChange={(e) => setNewVaultForm(prev => ({ ...prev, objective: e.target.value }))}
                  className="w-full p-3 bg-gray-700 border border-tertiary/50 rounded-lg text-supplement1 placeholder-gray-400 focus:outline-none focus:border-primary resize-none"
                  rows={3}
                  placeholder="Describe the purpose of this context"
                />
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <button
                onClick={() => setShowNewVaultForm(false)}
                className="flex-1 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-supplement1 rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleNewVault}
                disabled={!newVaultForm.name.trim() || loading}
                className="flex-1 px-4 py-2 bg-primary hover:bg-primary/80 text-gray-900 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Creating...' : 'Create Context'}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
