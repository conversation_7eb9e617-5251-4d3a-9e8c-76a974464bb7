import { ContextVault, ContextFolder, VaultRegistry } from '../types'
import { vaultUIManager } from './vaultUIManager'
import { toastService } from './toastService'

export interface CreateContextOptions {
  name: string
  objective?: string
  vaultType: 'Personal' | 'Work'
  color?: string
  icon?: string
}

export interface ContextVaultChangeListener {
  (vaults: ContextVault[], selectedContextId?: string): void
}

class ContextVaultService {
  private listeners: ContextVaultChangeListener[] = []
  private currentVaults: ContextVault[] = []
  private selectedContextId: string | null = null

  /**
   * Subscribe to context vault changes
   */
  subscribe(listener: ContextVaultChangeListener) {
    this.listeners.push(listener)
    // Immediately call with current data
    listener(this.currentVaults, this.selectedContextId || undefined)
    
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener)
    }
  }

  /**
   * Notify all listeners of changes
   */
  private notify() {
    this.listeners.forEach(listener => 
      listener(this.currentVaults, this.selectedContextId || undefined)
    )
  }

  /**
   * Load all vaults and contexts
   */
  async loadVaults(): Promise<ContextVault[]> {
    try {
      const registry = await vaultUIManager.getVaultRegistry()
      if (registry) {
        this.currentVaults = registry.vaults
        this.notify()
        return registry.vaults
      }
      return []
    } catch (error) {
      console.error('Error loading vaults:', error)
      toastService.error('Failed to Load Vaults', 'Could not load context vaults')
      return []
    }
  }

  /**
   * Get current vaults (cached)
   */
  getCurrentVaults(): ContextVault[] {
    return this.currentVaults
  }

  /**
   * Get all contexts from all vaults
   */
  getAllContexts(): ContextFolder[] {
    const contexts: ContextFolder[] = []
    for (const vault of this.currentVaults) {
      contexts.push(...vault.contexts)
    }
    return contexts
  }

  /**
   * Find context by ID
   */
  findContextById(contextId: string): { context: ContextFolder; vault: ContextVault } | null {
    for (const vault of this.currentVaults) {
      const context = vault.contexts.find(c => c.id === contextId)
      if (context) {
        return { context, vault }
      }
    }
    return null
  }

  /**
   * Set selected context
   */
  setSelectedContext(contextId: string | null) {
    this.selectedContextId = contextId
    this.notify()
  }

  /**
   * Get selected context
   */
  getSelectedContext(): ContextFolder | null {
    if (!this.selectedContextId) return null
    const result = this.findContextById(this.selectedContextId)
    return result?.context || null
  }

  /**
   * Create a new context in the specified vault
   */
  async createContext(options: CreateContextOptions): Promise<{ success: boolean; context?: ContextFolder; error?: string }> {
    try {
      // Find the target vault based on vaultType
      const targetVault = this.currentVaults.find(v => 
        v.name.toLowerCase().includes(options.vaultType.toLowerCase())
      )
      
      if (!targetVault) {
        const error = `${options.vaultType} vault not found`
        toastService.error('Vault Not Found', error)
        return { success: false, error }
      }
      
      // Create context folder path
      const contextId = options.name.toLowerCase().replace(/[^a-z0-9]/g, '-')
      const contextPath = `${targetVault.path}/${contextId}`
      
      // Create context directory
      const createDirResult = await window.electronAPI.vault.createDirectory(contextPath)
      if (!createDirResult.success) {
        toastService.error('Failed to Create Context', createDirResult.error || 'Unknown error')
        return { success: false, error: createDirResult.error }
      }
      
      // Create subdirectories
      await window.electronAPI.vault.createDirectory(`${contextPath}/documents`)
      await window.electronAPI.vault.createDirectory(`${contextPath}/images`)
      await window.electronAPI.vault.createDirectory(`${contextPath}/artifacts`)
      await window.electronAPI.vault.createDirectory(`${contextPath}/.context`)
      
      // Create master.md with objective
      const masterContent = options.objective 
        ? `# ${options.name}\n\n## Objective\n${options.objective}\n\n## Getting Started\nThis is your new context vault. You can:\n- Add documents to the documents folder\n- Upload images to the images folder\n- Generate artifacts that will be saved to the artifacts folder\n\nStart chatting with AI to begin working with your context!`
        : `# ${options.name}\n\n## Getting Started\nThis is your new context vault. You can:\n- Add documents to the documents folder\n- Upload images to the images folder\n- Generate artifacts that will be saved to the artifacts folder\n\nStart chatting with AI to begin working with your context!`
      
      const writeResult = await window.electronAPI.vault.writeFile(`${contextPath}/master.md`, masterContent)
      if (!writeResult.success) {
        toastService.error('Failed to Create Master File', writeResult.error || 'Unknown error')
        return { success: false, error: writeResult.error }
      }
      
      // Create context metadata
      const metadata = {
        id: contextId,
        name: options.name,
        created: new Date().toISOString(),
        description: options.objective || `Context vault for ${options.name}`,
        color: options.color || (options.vaultType === 'Personal' ? '#8AB0BB' : '#FF8383'),
        icon: options.icon || 'fa-folder',
        contextType: 'project'
      }
      
      await window.electronAPI.vault.writeFile(
        `${contextPath}/.context/metadata.json`, 
        JSON.stringify(metadata, null, 2)
      )
      
      // Reload vaults to get the new context
      await this.loadVaults()
      
      // Find the newly created context
      const newContextResult = this.findContextById(contextId)
      if (newContextResult) {
        // Set as selected context
        this.setSelectedContext(contextId)
        
        // Show success toast
        toastService.contextCreated(options.name, options.vaultType)
        
        return { success: true, context: newContextResult.context }
      }
      
      return { success: true }
      
    } catch (error: any) {
      console.error('Error creating context:', error)
      toastService.contextCreationFailed(error.message || 'Unknown error')
      return { success: false, error: error.message }
    }
  }

  /**
   * Refresh vault data (rescan all vaults)
   */
  async refreshVaults(): Promise<void> {
    try {
      const registry = await vaultUIManager.scanVaults()
      if (registry) {
        this.currentVaults = registry.vaults
        this.notify()
        
        const contextCount = this.getAllContexts().length
        toastService.vaultSyncCompleted(contextCount)
      }
    } catch (error) {
      console.error('Error refreshing vaults:', error)
      toastService.error('Sync Failed', 'Could not refresh vault data')
    }
  }

  /**
   * Initialize the service (load initial data)
   */
  async initialize(): Promise<void> {
    await this.loadVaults()
  }
}

export const contextVaultService = new ContextVaultService()
